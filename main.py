#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机邮箱桌面程序
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.main_window import MainWindow
from src.core.config_manager import ConfigManager

def main():
    """主程序入口"""
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        root = tk.Tk()
        app = MainWindow(root, config_manager)
        
        # 启动GUI主循环
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
