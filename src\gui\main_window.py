#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from typing import Optional

from ..core.config_manager import ConfigManager
from ..core.email_generator import EmailGenerator
from ..services.temp_mail_service import TempMailService
from .settings_window import SettingsWindow
from .history_window import HistoryWindow

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, config_manager: ConfigManager):
        """
        初始化主窗口
        
        Args:
            root: Tkinter根窗口
            config_manager: 配置管理器
        """
        self.root = root
        self.config_manager = config_manager
        self.email_generator = EmailGenerator()
        self.temp_mail_service = TempMailService()
        
        self.current_email = None
        self.current_pin = None
        self.auto_refresh_thread = None
        self.auto_refresh_running = False
        
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("随机邮箱程序 v1.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 创建邮箱生成区域
        self.create_email_generation_area(main_frame)
        
        # 创建控制按钮区域
        self.create_control_buttons_area(main_frame)
        
        # 创建邮件列表区域
        self.create_email_list_area(main_frame)
        
        # 创建状态栏
        self.create_status_bar(main_frame)
    
    def create_email_generation_area(self, parent):
        """创建邮箱生成区域"""
        # 邮箱生成框架
        email_frame = ttk.LabelFrame(parent, text="邮箱生成", padding="10")
        email_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        email_frame.columnconfigure(1, weight=1)
        
        # 域名选择
        ttk.Label(email_frame, text="域名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.domain_var = tk.StringVar()
        self.domain_combo = ttk.Combobox(email_frame, textvariable=self.domain_var, width=20)
        self.domain_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 刷新域名按钮
        ttk.Button(email_frame, text="刷新域名", command=self.refresh_domains).grid(row=0, column=2, padx=(5, 0))
        
        # 自定义前缀
        ttk.Label(email_frame, text="自定义前缀:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.prefix_var = tk.StringVar()
        self.prefix_entry = ttk.Entry(email_frame, textvariable=self.prefix_var)
        self.prefix_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 0), padx=(0, 10))
        
        # 生成邮箱按钮
        ttk.Button(email_frame, text="生成邮箱", command=self.generate_email).grid(row=1, column=2, padx=(5, 0), pady=(10, 0))
        
        # 当前邮箱显示
        ttk.Label(email_frame, text="当前邮箱:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.current_email_var = tk.StringVar(value="未生成")
        current_email_label = ttk.Label(email_frame, textvariable=self.current_email_var, foreground="blue")
        current_email_label.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 复制邮箱按钮
        ttk.Button(email_frame, text="复制邮箱", command=self.copy_email).grid(row=2, column=2, padx=(5, 0), pady=(10, 0))
        
        # PIN码输入/显示
        ttk.Label(email_frame, text="PIN码:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.pin_var = tk.StringVar()
        self.pin_entry = ttk.Entry(email_frame, textvariable=self.pin_var, width=15)
        self.pin_entry.grid(row=3, column=1, sticky=tk.W, pady=(10, 0))

        # PIN码说明
        pin_info = ttk.Label(email_frame, text="(tempmail.plus需要自定义PIN码)", font=('TkDefaultFont', 8))
        pin_info.grid(row=4, column=1, sticky=tk.W, pady=(2, 0))
        
        # 复制PIN按钮
        ttk.Button(email_frame, text="复制PIN", command=self.copy_pin).grid(row=3, column=2, padx=(5, 0), pady=(10, 0))
    
    def create_control_buttons_area(self, parent):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 刷新邮件按钮
        ttk.Button(control_frame, text="刷新邮件", command=self.refresh_emails).pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动刷新开关
        self.auto_refresh_var = tk.BooleanVar()
        auto_refresh_check = ttk.Checkbutton(control_frame, text="自动刷新", variable=self.auto_refresh_var, command=self.toggle_auto_refresh)
        auto_refresh_check.pack(side=tk.LEFT, padx=(0, 10))
        
        # 设置按钮
        ttk.Button(control_frame, text="设置", command=self.open_settings).pack(side=tk.LEFT, padx=(0, 10))
        
        # 历史记录按钮
        ttk.Button(control_frame, text="历史记录", command=self.show_history).pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空邮件按钮
        ttk.Button(control_frame, text="清空邮件", command=self.clear_emails).pack(side=tk.RIGHT)
    
    def create_email_list_area(self, parent):
        """创建邮件列表区域"""
        # 邮件列表框架
        email_list_frame = ttk.LabelFrame(parent, text="邮件列表", padding="5")
        email_list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        email_list_frame.columnconfigure(0, weight=1)
        email_list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示邮件列表
        columns = ('from', 'subject', 'date', 'preview')
        self.email_tree = ttk.Treeview(email_list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        self.email_tree.heading('from', text='发件人')
        self.email_tree.heading('subject', text='主题')
        self.email_tree.heading('date', text='日期')
        self.email_tree.heading('preview', text='预览')
        
        # 设置列宽
        self.email_tree.column('from', width=150)
        self.email_tree.column('subject', width=200)
        self.email_tree.column('date', width=120)
        self.email_tree.column('preview', width=300)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(email_list_frame, orient=tk.VERTICAL, command=self.email_tree.yview)
        self.email_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.email_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.email_tree.bind('<Double-1>', self.on_email_double_click)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 邮件计数
        self.email_count_var = tk.StringVar(value="邮件: 0")
        count_label = ttk.Label(status_frame, textvariable=self.email_count_var)
        count_label.grid(row=0, column=1, sticky=tk.E)
    
    def load_initial_data(self):
        """加载初始数据"""
        # 加载域名列表
        self.refresh_domains()
        
        # 设置默认域名
        default_domain = self.config_manager.get_preferred_domain()
        if default_domain in self.domain_combo['values']:
            self.domain_var.set(default_domain)
        
        # 加载自定义前缀
        custom_prefix = self.config_manager.get_custom_email_prefix()
        if custom_prefix:
            self.prefix_var.set(custom_prefix)
    
    def refresh_domains(self):
        """刷新域名列表"""
        try:
            self.status_var.set("正在获取域名列表...")
            domains = self.temp_mail_service.get_available_domains()
            self.domain_combo['values'] = domains
            if domains and not self.domain_var.get():
                self.domain_var.set(domains[0])
            self.status_var.set("域名列表已更新")
        except Exception as e:
            messagebox.showerror("错误", f"获取域名列表失败: {str(e)}")
            self.status_var.set("获取域名失败")
    
    def generate_email(self):
        """生成新邮箱"""
        try:
            domain = self.domain_var.get()
            if not domain:
                messagebox.showwarning("警告", "请选择一个域名")
                return

            custom_prefix = self.prefix_var.get().strip()
            custom_pin = self.pin_var.get().strip()

            # 保存自定义前缀到配置
            if custom_prefix:
                self.config_manager.set_custom_email_prefix(custom_prefix)

            # 检查是否是tempmail.plus服务
            is_tempmail_plus = domain in ['tempmail.plus', 'tempmail.email', 'tempmail.ninja', 'tempmail.dev', 'tempmail.io', 'tempmail.co']

            if is_tempmail_plus:
                # 对于tempmail.plus，需要用户提供PIN码
                if not custom_pin:
                    messagebox.showwarning("警告", "tempmail.plus服务需要设置PIN码")
                    return

                # 生成*@域名格式的邮箱
                email = self.email_generator.generate_email_address(domain, custom_prefix, use_asterisk=True)
                pin = self.email_generator.set_custom_pin(custom_pin)

                # 保存PIN码到配置
                self.config_manager.set_pin_code(custom_pin)

            else:
                # 对于其他服务，使用传统方式
                email = self.email_generator.generate_email_address(domain, custom_prefix, use_asterisk=False)
                pin = self.email_generator.generate_pin_code()

            # 更新显示
            self.current_email = email
            self.current_pin = pin
            self.current_email_var.set(email)

            # 如果不是tempmail.plus，更新PIN码显示
            if not is_tempmail_plus:
                self.pin_var.set(pin)

            # 清空邮件列表
            self.clear_emails()

            if is_tempmail_plus:
                self.status_var.set(f"已生成tempmail.plus邮箱: {email} (PIN: {pin})")
            else:
                self.status_var.set(f"已生成邮箱: {email}")

        except Exception as e:
            messagebox.showerror("错误", f"生成邮箱失败: {str(e)}")
    
    def copy_email(self):
        """复制邮箱地址到剪贴板"""
        if self.current_email:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.current_email)
            self.status_var.set("邮箱地址已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "请先生成邮箱")
    
    def copy_pin(self):
        """复制PIN码到剪贴板"""
        if self.current_pin:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.current_pin)
            self.status_var.set("PIN码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "请先生成邮箱")
    
    def refresh_emails(self):
        """刷新邮件列表"""
        if not self.current_email:
            messagebox.showwarning("警告", "请先生成邮箱")
            return
        
        try:
            self.status_var.set("正在获取邮件...")
            emails = self.temp_mail_service.get_emails(self.current_email, self.current_pin or "")
            
            # 清空现有列表
            for item in self.email_tree.get_children():
                self.email_tree.delete(item)
            
            # 添加邮件到列表
            for email in emails:
                self.email_tree.insert('', 'end', values=(
                    email.get('from', ''),
                    email.get('subject', ''),
                    email.get('date', ''),
                    email.get('preview', '')[:50] + '...' if len(email.get('preview', '')) > 50 else email.get('preview', '')
                ))
            
            self.email_count_var.set(f"邮件: {len(emails)}")
            self.status_var.set(f"已获取 {len(emails)} 封邮件")
            
        except Exception as e:
            messagebox.showerror("错误", f"获取邮件失败: {str(e)}")
            self.status_var.set("获取邮件失败")
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()
    
    def start_auto_refresh(self):
        """开始自动刷新"""
        if not self.auto_refresh_running:
            self.auto_refresh_running = True
            self.auto_refresh_thread = threading.Thread(target=self._auto_refresh_worker, daemon=True)
            self.auto_refresh_thread.start()
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh_running = False
    
    def _auto_refresh_worker(self):
        """自动刷新工作线程"""
        while self.auto_refresh_running:
            if self.current_email:
                try:
                    self.root.after(0, self.refresh_emails)
                except:
                    pass
            
            # 等待刷新间隔
            interval = self.config_manager.get_auto_refresh_interval()
            for _ in range(interval):
                if not self.auto_refresh_running:
                    break
                time.sleep(1)
    
    def clear_emails(self):
        """清空邮件列表"""
        for item in self.email_tree.get_children():
            self.email_tree.delete(item)
        self.email_count_var.set("邮件: 0")
    
    def on_email_double_click(self, event):
        """邮件双击事件处理"""
        selection = self.email_tree.selection()
        if selection:
            item = self.email_tree.item(selection[0])
            values = item['values']
            if values:
                self.show_email_content(values[0], values[1])  # 传递发件人和主题
    
    def show_email_content(self, sender: str, subject: str):
        """显示邮件内容"""
        # 创建新窗口显示邮件内容
        content_window = tk.Toplevel(self.root)
        content_window.title(f"邮件内容 - {subject}")
        content_window.geometry("600x400")
        
        # 邮件信息
        info_frame = ttk.Frame(content_window, padding="10")
        info_frame.pack(fill=tk.X)
        
        ttk.Label(info_frame, text=f"发件人: {sender}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"主题: {subject}").pack(anchor=tk.W)
        
        # 邮件内容
        content_text = scrolledtext.ScrolledText(content_window, wrap=tk.WORD, padding="10")
        content_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 这里应该获取实际的邮件内容
        content_text.insert(tk.END, "邮件内容加载中...\n\n这是一个示例邮件内容。")
        content_text.config(state=tk.DISABLED)
    
    def open_settings(self):
        """打开设置窗口"""
        try:
            SettingsWindow(self.root, self.config_manager)
        except Exception as e:
            messagebox.showerror("错误", f"打开设置窗口失败: {str(e)}")

    def show_history(self):
        """显示历史记录"""
        try:
            HistoryWindow(self.root, self.email_generator)
        except Exception as e:
            messagebox.showerror("错误", f"打开历史记录窗口失败: {str(e)}")
