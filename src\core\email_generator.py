#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱生成器
负责生成随机邮箱地址和管理邮箱状态
"""

import random
import string
import uuid
import hashlib
import re
from typing import Optional, List
from datetime import datetime, timedelta

class EmailGenerator:
    """邮箱生成器类"""
    
    def __init__(self):
        """初始化邮箱生成器"""
        self.current_email = None
        self.current_pin = None
        self.actual_prefix = None  # 实际的邮箱前缀（用于*@域名格式）
        self.email_history = []
    
    def generate_random_string(self, length: int = 8) -> str:
        """
        生成随机字符串
        
        Args:
            length: 字符串长度
            
        Returns:
            随机字符串
        """
        characters = string.ascii_lowercase + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def generate_email_address(self, domain: str, custom_prefix: str = None, use_asterisk: bool = True) -> str:
        """
        生成邮箱地址

        Args:
            domain: 邮箱域名
            custom_prefix: 自定义前缀
            use_asterisk: 是否使用*@域名格式（用于tempmail.plus）

        Returns:
            完整的邮箱地址
        """
        # 确保域名格式正确
        if domain.startswith('@'):
            domain = domain[1:]

        if use_asterisk and domain in ['tempmail.plus', 'tempmail.email', 'tempmail.ninja', 'tempmail.dev', 'tempmail.io', 'tempmail.co']:
            # 对于tempmail.plus服务，使用*@域名格式
            email = f"*@{domain}"
            # 保存实际的前缀用于PIN码关联
            if custom_prefix and self.is_valid_email_prefix(custom_prefix):
                self.actual_prefix = custom_prefix
            else:
                self.actual_prefix = self.generate_random_string(random.randint(6, 12))
        else:
            # 对于其他服务，使用传统格式
            if custom_prefix and self.is_valid_email_prefix(custom_prefix):
                prefix = custom_prefix
            else:
                # 生成随机前缀
                prefix = self.generate_random_string(random.randint(6, 12))

            email = f"{prefix}@{domain}"
            self.actual_prefix = prefix

        self.current_email = email

        # 添加到历史记录
        self.add_to_history(email)

        return email
    
    def set_custom_pin(self, pin: str) -> str:
        """
        设置自定义PIN码（用于tempmail.plus）

        Args:
            pin: 用户设置的PIN码

        Returns:
            PIN码
        """
        self.current_pin = pin
        return pin

    def generate_pin_code(self, length: int = 6) -> str:
        """
        生成随机PIN码（用于其他服务）

        Args:
            length: PIN码长度

        Returns:
            PIN码
        """
        pin = ''.join(random.choice(string.digits) for _ in range(length))
        self.current_pin = pin
        return pin
    
    def generate_secure_pin(self, email: str, secret_key: str = None) -> str:
        """
        基于邮箱地址生成安全PIN码
        
        Args:
            email: 邮箱地址
            secret_key: 密钥（可选）
            
        Returns:
            安全PIN码
        """
        if not secret_key:
            secret_key = str(uuid.uuid4())
        
        # 使用SHA256生成哈希
        hash_input = f"{email}{secret_key}{datetime.now().strftime('%Y%m%d')}"
        hash_object = hashlib.sha256(hash_input.encode())
        hash_hex = hash_object.hexdigest()
        
        # 提取数字作为PIN码
        pin = ''.join(filter(str.isdigit, hash_hex))[:6]
        
        # 如果数字不够，补充随机数字
        while len(pin) < 6:
            pin += str(random.randint(0, 9))
        
        self.current_pin = pin
        return pin
    
    def is_valid_email_prefix(self, prefix: str) -> bool:
        """
        验证邮箱前缀是否有效
        
        Args:
            prefix: 邮箱前缀
            
        Returns:
            是否有效
        """
        if not prefix:
            return False
        
        # 检查长度
        if len(prefix) < 3 or len(prefix) > 20:
            return False
        
        # 检查字符（只允许字母、数字、点、下划线、连字符）
        pattern = r'^[a-zA-Z0-9._-]+$'
        if not re.match(pattern, prefix):
            return False
        
        # 不能以点开头或结尾
        if prefix.startswith('.') or prefix.endswith('.'):
            return False
        
        # 不能有连续的点
        if '..' in prefix:
            return False
        
        return True
    
    def add_to_history(self, email: str):
        """
        添加邮箱到历史记录
        
        Args:
            email: 邮箱地址
        """
        history_item = {
            'email': email,
            'created_at': datetime.now(),
            'pin': self.current_pin
        }
        
        self.email_history.append(history_item)
        
        # 保持历史记录不超过100条
        if len(self.email_history) > 100:
            self.email_history = self.email_history[-100:]
    
    def get_email_history(self, limit: int = 20) -> List[dict]:
        """
        获取邮箱历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            历史记录列表
        """
        return self.email_history[-limit:] if self.email_history else []
    
    def clear_history(self):
        """清空历史记录"""
        self.email_history.clear()
    
    def get_current_email(self) -> Optional[str]:
        """获取当前邮箱地址"""
        return self.current_email
    
    def get_current_pin(self) -> Optional[str]:
        """获取当前PIN码"""
        return self.current_pin

    def get_actual_prefix(self) -> Optional[str]:
        """获取实际的邮箱前缀（用于*@域名格式）"""
        return self.actual_prefix

    def validate_pin(self, pin: str) -> bool:
        """验证PIN码是否正确"""
        return self.current_pin == pin
    
    def validate_pin(self, input_pin: str) -> bool:
        """
        验证PIN码
        
        Args:
            input_pin: 输入的PIN码
            
        Returns:
            是否匹配
        """
        return self.current_pin and input_pin == self.current_pin
