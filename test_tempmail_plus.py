#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tempmail.plus功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.email_generator import EmailGenerator
from src.services.temp_mail_service import TempMailService

def test_tempmail_plus_email_generation():
    """测试tempmail.plus邮箱生成"""
    print("=== 测试Tempmail.plus邮箱生成 ===")
    
    generator = EmailGenerator()
    
    # 测试*@域名格式生成
    email1 = generator.generate_email_address("tempmail.plus", use_asterisk=True)
    print(f"生成的邮箱: {email1}")
    print(f"实际前缀: {generator.get_actual_prefix()}")
    
    # 测试自定义前缀
    email2 = generator.generate_email_address("tempmail.plus", "mytest", use_asterisk=True)
    print(f"自定义前缀邮箱: {email2}")
    print(f"实际前缀: {generator.get_actual_prefix()}")
    
    # 测试自定义PIN码
    pin = generator.set_custom_pin("123456")
    print(f"自定义PIN码: {pin}")
    
    print("✓ Tempmail.plus邮箱生成测试通过\n")

def test_tempmail_plus_config():
    """测试tempmail.plus配置"""
    print("=== 测试Tempmail.plus配置 ===")
    
    config = ConfigManager("test_tempmail_config.ini")
    
    # 测试默认域名
    preferred_domain = config.get_preferred_domain()
    print(f"首选域名: {preferred_domain}")
    
    # 测试域名列表
    domains = config.get_custom_domains()
    print(f"域名列表: {domains}")
    
    # 测试PIN码设置
    config.set_pin_code("654321")
    saved_pin = config.get_pin_code()
    print(f"保存的PIN码: {saved_pin}")
    
    print("✓ Tempmail.plus配置测试通过\n")

def test_tempmail_plus_service():
    """测试tempmail.plus服务"""
    print("=== 测试Tempmail.plus服务 ===")
    
    service = TempMailService()
    
    # 测试域名获取
    domains = service.get_available_domains("tempmail.plus")
    print(f"Tempmail.plus域名: {domains}")
    
    # 测试邮箱创建
    email_info = service.create_email("tempmail.plus", "testuser")
    print(f"创建的邮箱信息: {email_info}")
    
    # 测试邮件获取（模拟）
    emails = service.get_emails("*@tempmail.plus", "123456")
    print(f"邮件数量: {len(emails)}")
    
    print("✓ Tempmail.plus服务测试通过\n")

def test_email_format_validation():
    """测试邮箱格式验证"""
    print("=== 测试邮箱格式验证 ===")
    
    generator = EmailGenerator()
    
    # 测试不同域名的格式
    tempmail_domains = ["tempmail.plus", "tempmail.email", "tempmail.ninja"]
    other_domains = ["temp-mail.org", "guerrillamail.com"]
    
    print("Tempmail.plus域名格式:")
    for domain in tempmail_domains:
        email = generator.generate_email_address(domain, "test", use_asterisk=True)
        print(f"  {domain}: {email}")
    
    print("\n其他域名格式:")
    for domain in other_domains:
        email = generator.generate_email_address(domain, "test", use_asterisk=False)
        print(f"  {domain}: {email}")
    
    print("✓ 邮箱格式验证测试通过\n")

def test_pin_functionality():
    """测试PIN码功能"""
    print("=== 测试PIN码功能 ===")
    
    generator = EmailGenerator()
    
    # 测试自定义PIN码
    custom_pin = "987654"
    set_pin = generator.set_custom_pin(custom_pin)
    print(f"设置的PIN码: {set_pin}")
    print(f"获取的PIN码: {generator.get_current_pin()}")
    
    # 测试PIN码验证
    is_valid = generator.validate_pin("987654")
    is_invalid = generator.validate_pin("123456")
    print(f"正确PIN码验证: {is_valid}")
    print(f"错误PIN码验证: {is_invalid}")
    
    # 测试随机PIN码生成
    random_pin = generator.generate_pin_code()
    print(f"随机PIN码: {random_pin}")
    
    print("✓ PIN码功能测试通过\n")

def test_integration_workflow():
    """测试完整工作流程"""
    print("=== 测试完整工作流程 ===")
    
    # 初始化组件
    config = ConfigManager("test_tempmail_config.ini")
    generator = EmailGenerator()
    service = TempMailService()
    
    print("1. 配置tempmail.plus...")
    config.set_preferred_domain("tempmail.plus")
    config.set_pin_code("111222")
    
    print("2. 生成邮箱...")
    domain = config.get_preferred_domain()
    custom_prefix = "testuser"
    email = generator.generate_email_address(domain, custom_prefix, use_asterisk=True)
    pin = generator.set_custom_pin(config.get_pin_code())
    
    print(f"   域名: {domain}")
    print(f"   邮箱: {email}")
    print(f"   PIN码: {pin}")
    print(f"   实际前缀: {generator.get_actual_prefix()}")
    
    print("3. 模拟获取邮件...")
    emails = service.get_emails(email, pin)
    print(f"   邮件数量: {len(emails)}")
    
    print("4. 验证配置持久化...")
    saved_domain = config.get_preferred_domain()
    saved_pin = config.get_pin_code()
    print(f"   保存的域名: {saved_domain}")
    print(f"   保存的PIN码: {saved_pin}")
    
    print("✓ 完整工作流程测试通过\n")

def cleanup():
    """清理测试文件"""
    test_files = ["test_tempmail_config.ini"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"已删除测试文件: {file}")

def main():
    """主测试函数"""
    print("开始测试Tempmail.plus功能...\n")
    
    try:
        test_tempmail_plus_email_generation()
        test_tempmail_plus_config()
        test_tempmail_plus_service()
        test_email_format_validation()
        test_pin_functionality()
        test_integration_workflow()
        
        print("🎉 所有Tempmail.plus功能测试通过！")
        print("\n主要功能:")
        print("✅ 支持*@域名格式的邮箱生成")
        print("✅ 支持用户自定义PIN码")
        print("✅ 支持tempmail.plus服务集成")
        print("✅ 支持配置持久化")
        print("✅ 支持多种tempmail.plus域名")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup()

if __name__ == "__main__":
    main()
