@echo off
chcp 65001 >nul
title 随机邮箱程序

echo ================================
echo     随机邮箱桌面程序 v1.0
echo ================================
echo.

echo 正在启动程序...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 检测到缺少依赖包，需要安装...
    echo 你可以选择以下方式安装:
    echo   1. 自动安装 (推荐)
    echo   2. 手动安装
    echo.
    set /p choice="请选择 (1/2): "

    if "%choice%"=="1" (
        echo 正在自动安装依赖包...
        python install_requirements.py
        if errorlevel 1 (
            echo 自动安装失败，请尝试手动安装
            echo 运行命令: python install_requirements.py
            pause
            exit /b 1
        )
    ) else (
        echo 请手动运行以下命令安装依赖:
        echo   python install_requirements.py
        echo 或者:
        echo   pip install requests urllib3 certifi
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动随机邮箱程序...
echo.
python main.py

REM 程序结束后暂停
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
)
echo.
echo 程序已退出
pause
