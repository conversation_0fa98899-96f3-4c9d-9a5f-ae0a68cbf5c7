# Tempmail.plus 使用说明

## 🎯 新功能概述

程序现在已经完全支持tempmail.plus服务，具有以下特色功能：

### ✨ 主要特性

1. **`*@域名`格式邮箱** - 支持tempmail.plus的特殊邮箱格式
2. **自定义PIN码** - 用户可以设置自己的PIN码来访问加密邮箱
3. **多域名支持** - 支持tempmail.plus的多个域名
4. **智能识别** - 自动识别tempmail.plus服务并使用相应功能

## 📧 邮箱格式说明

### Tempmail.plus格式
- **显示格式**: `*@tempmail.plus`
- **实际使用**: 通过PIN码关联到具体邮箱
- **优势**: 更好的隐私保护和加密支持

### 传统格式
- **显示格式**: `<EMAIL>`
- **使用场景**: 其他邮箱服务提供商

## 🔐 PIN码功能

### 对于Tempmail.plus服务
- **必须设置**: 使用tempmail.plus时必须输入PIN码
- **用户自定义**: PIN码由用户自己设置，不是随机生成
- **访问控制**: PIN码用于访问和解密邮箱内容
- **持久保存**: PIN码会保存到配置文件中

### 对于其他服务
- **自动生成**: 系统自动生成随机PIN码
- **可选使用**: 根据服务提供商要求决定是否使用

## 🚀 使用步骤

### 1. 选择Tempmail.plus服务

1. 启动程序
2. 在域名下拉框中选择以下任一域名：
   - `tempmail.plus`
   - `tempmail.email`
   - `tempmail.ninja`
   - `tempmail.dev`
   - `tempmail.io`
   - `tempmail.co`

### 2. 设置PIN码

1. 在"PIN码"输入框中输入你的自定义PIN码
2. PIN码建议使用6-12位数字或字母组合
3. 确保PIN码容易记住但足够安全

### 3. 生成邮箱

1. （可选）在"自定义前缀"中输入邮箱前缀
2. 点击"生成邮箱"按钮
3. 系统会生成`*@域名`格式的邮箱地址

### 4. 接收邮件

1. 点击"刷新邮件"按钮
2. 系统使用你的PIN码访问邮箱
3. 查看接收到的邮件列表

## 🔧 配置说明

### 默认设置
- **首选域名**: tempmail.plus
- **域名列表**: 包含所有tempmail.plus域名
- **PIN码**: 用户自定义，保存在配置文件中

### 配置文件位置
- **文件名**: `config.ini`
- **位置**: 程序根目录
- **内容**: 包含域名、PIN码、刷新间隔等设置

## 📋 功能对比

| 功能 | Tempmail.plus | 其他服务 |
|------|---------------|----------|
| 邮箱格式 | `*@域名` | `用户名@域名` |
| PIN码 | 用户自定义 | 自动生成 |
| 加密支持 | ✅ | ❌ |
| 隐私保护 | 🔒 高 | 🔓 中等 |
| 域名数量 | 6个 | 多个 |

## 🎮 操作示例

### 示例1：使用tempmail.plus生成邮箱

1. **选择域名**: `tempmail.plus`
2. **设置PIN码**: `123456`
3. **自定义前缀**: `mytest`（可选）
4. **点击生成**: 得到 `*@tempmail.plus`
5. **刷新邮件**: 使用PIN码 `123456` 访问邮箱

### 示例2：使用传统服务

1. **选择域名**: `temp-mail.org`
2. **自定义前缀**: `testuser`（可选）
3. **点击生成**: 得到 `<EMAIL>`
4. **自动生成PIN**: 系统生成随机PIN码
5. **刷新邮件**: 使用生成的PIN码访问

## ⚠️ 注意事项

### 重要提醒
1. **PIN码安全**: 请妥善保管你的PIN码
2. **域名选择**: 确保选择正确的tempmail.plus域名
3. **网络连接**: 需要稳定的网络连接访问API
4. **服务可用性**: tempmail.plus服务的可用性取决于官方服务器

### 故障排除
1. **无法生成邮箱**: 检查是否设置了PIN码
2. **无法接收邮件**: 确认PIN码输入正确
3. **连接错误**: 检查网络连接和防火墙设置
4. **API错误**: 可能是服务器临时不可用，稍后重试

## 🔄 版本更新

### 新增功能
- ✅ 支持tempmail.plus服务
- ✅ `*@域名`格式邮箱生成
- ✅ 用户自定义PIN码
- ✅ 智能服务识别
- ✅ 配置持久化

### 兼容性
- ✅ 完全兼容原有功能
- ✅ 支持所有原有邮箱服务
- ✅ 保持原有界面布局
- ✅ 配置文件向后兼容

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看程序状态栏的错误信息
2. 检查网络连接
3. 确认PIN码设置正确
4. 尝试切换到其他域名
5. 重启程序重新配置

---

**享受使用tempmail.plus的安全邮箱服务！** 🎉
