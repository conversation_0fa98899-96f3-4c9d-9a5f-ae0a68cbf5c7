@echo off
chcp 65001 >nul
title 安装依赖包

echo ================================
echo     随机邮箱程序 - 依赖安装
echo ================================
echo.

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo.
echo 当前Python环境信息:
python -c "import sys; print(f'Python路径: {sys.executable}')"
python -c "import sys; print(f'Python版本: {sys.version}')"

echo.
echo 正在升级pip...
python -m pip install --upgrade pip

echo.
echo 正在安装依赖包...
echo.

echo [1/3] 安装requests (HTTP请求库)...
pip install requests>=2.25.0
if errorlevel 1 (
    echo 警告: requests安装失败，尝试使用国内镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple requests>=2.25.0
)

echo.
echo [2/3] 安装urllib3 (HTTP客户端库)...
pip install urllib3>=1.26.0
if errorlevel 1 (
    echo 警告: urllib3安装失败，尝试使用国内镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple urllib3>=1.26.0
)

echo.
echo [3/3] 安装certifi (SSL证书库)...
pip install certifi>=2021.5.25
if errorlevel 1 (
    echo 警告: certifi安装失败，尝试使用国内镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple certifi>=2021.5.25
)

echo.
echo ================================
echo 验证安装结果...
echo ================================

echo.
echo 检查已安装的包:
pip list | findstr -i "requests urllib3 certifi"

echo.
echo 测试导入模块:
python -c "import requests; print('✓ requests 导入成功')" 2>nul || echo "✗ requests 导入失败"
python -c "import urllib3; print('✓ urllib3 导入成功')" 2>nul || echo "✗ urllib3 导入失败"
python -c "import certifi; print('✓ certifi 导入成功')" 2>nul || echo "✗ certifi 导入失败"
python -c "import tkinter; print('✓ tkinter 导入成功')" 2>nul || echo "✗ tkinter 导入失败"

echo.
echo 测试核心功能:
python -c "
try:
    import requests
    import tkinter as tk
    import json
    import configparser
    import threading
    import datetime
    import re
    import base64
    import hashlib
    import uuid
    print('✓ 所有核心模块导入成功！')
    print('✓ 程序依赖安装完成，可以正常运行')
except ImportError as e:
    print(f'✗ 模块导入失败: {e}')
"

echo.
echo ================================
echo 安装完成！
echo ================================
echo.
echo 现在你可以运行程序了:
echo   方法1: 双击 run.bat
echo   方法2: 运行 python main.py
echo.
pause
