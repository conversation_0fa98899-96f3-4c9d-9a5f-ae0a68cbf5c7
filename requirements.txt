# 网络请求库
requests>=2.25.0

# GUI相关 (tkinter是Python内置的，不需要安装)
# tkinter - 内置GUI库

# 数据处理
# json - 内置JSON处理库
# configparser - 内置配置文件处理库

# 系统和工具库
# os - 内置操作系统接口
# sys - 内置系统相关参数和函数
# threading - 内置线程库
# datetime - 内置日期时间库
# time - 内置时间库
# re - 内置正则表达式库
# base64 - 内置Base64编码库
# hashlib - 内置哈希库
# uuid - 内置UUID生成库
# random - 内置随机数库
# string - 内置字符串处理库
# typing - 内置类型提示库

# 如果需要更好的HTTP请求处理，可以添加：
urllib3>=1.26.0
certifi>=2021.5.25

# 如果需要更好的JSON处理，可以添加：
# simplejson>=3.17.0

# 如果需要加密功能增强，可以添加：
# cryptography>=3.4.0

# 开发和调试工具（可选）
# pytest>=6.0.0
# flake8>=3.8.0
