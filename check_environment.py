#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
检查所有依赖是否正确安装
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🐍 Python环境检查")
    print("=" * 40)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    if sys.version_info >= (3, 7):
        print("✅ Python版本符合要求 (>= 3.7)")
        return True
    else:
        print("❌ Python版本过低，需要3.7或更高版本")
        return False

def check_modules():
    """检查所有必需模块"""
    print("\n📦 模块依赖检查")
    print("=" * 40)
    
    # 必需的外部模块
    external_modules = [
        ("requests", "HTTP请求库"),
        ("urllib3", "HTTP客户端库"),
        ("certifi", "SSL证书库")
    ]
    
    # 内置模块
    builtin_modules = [
        ("tkinter", "GUI界面库"),
        ("json", "JSON处理库"),
        ("configparser", "配置文件库"),
        ("threading", "线程库"),
        ("datetime", "日期时间库"),
        ("re", "正则表达式库"),
        ("hashlib", "哈希库"),
        ("uuid", "UUID库"),
        ("base64", "Base64编码库"),
        ("os", "操作系统接口"),
        ("sys", "系统参数库")
    ]
    
    success_count = 0
    total_count = len(external_modules) + len(builtin_modules)
    
    print("外部依赖包:")
    for module, description in external_modules:
        try:
            __import__(module)
            print(f"  ✅ {module:<12} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module:<12} - {description} (错误: {e})")
    
    print("\n内置模块:")
    for module, description in builtin_modules:
        try:
            __import__(module)
            print(f"  ✅ {module:<12} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module:<12} - {description} (错误: {e})")
    
    print(f"\n模块检查结果: {success_count}/{total_count} 个模块可用")
    return success_count == total_count

def check_core_functionality():
    """检查核心功能"""
    print("\n🔧 核心功能检查")
    print("=" * 40)
    
    try:
        # 测试配置管理
        import configparser
        config = configparser.ConfigParser()
        print("✅ 配置管理功能正常")
        
        # 测试网络请求
        import requests
        print("✅ 网络请求功能正常")
        
        # 测试GUI (不显示窗口)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ GUI功能正常")
        
        # 测试JSON处理
        import json
        test_data = {"test": "data"}
        json_str = json.dumps(test_data)
        parsed_data = json.loads(json_str)
        print("✅ JSON处理功能正常")
        
        # 测试哈希功能
        import hashlib
        test_hash = hashlib.md5("test".encode()).hexdigest()
        print("✅ 哈希功能正常")
        
        print("✅ 所有核心功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        return False

def check_program_files():
    """检查程序文件"""
    print("\n📁 程序文件检查")
    print("=" * 40)
    
    required_files = [
        "main.py",
        "src/__init__.py",
        "src/core/__init__.py",
        "src/core/config_manager.py",
        "src/core/email_generator.py",
        "src/gui/__init__.py",
        "src/gui/main_window.py",
        "src/gui/settings_window.py",
        "src/gui/history_window.py",
        "src/services/__init__.py",
        "src/services/temp_mail_service.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺失 {len(missing_files)} 个文件")
        return False
    else:
        print(f"\n✅ 所有程序文件完整")
        return True

def provide_solutions():
    """提供解决方案"""
    print("\n🛠️ 问题解决方案")
    print("=" * 40)
    print("如果遇到模块缺失问题，请运行以下命令:")
    print()
    print("1. 安装外部依赖:")
    print("   pip install requests urllib3 certifi")
    print()
    print("2. 或者使用conda安装:")
    print("   conda install -c conda-forge requests urllib3 certifi")
    print()
    print("3. 或者运行自动安装脚本:")
    print("   python install_requirements.py")
    print()
    print("4. 如果tkinter缺失 (Linux):")
    print("   sudo apt-get install python3-tk")
    print()

def main():
    """主函数"""
    print("🔍 随机邮箱程序 - 环境检查")
    print("=" * 50)
    
    # 检查各个组件
    python_ok = check_python_version()
    modules_ok = check_modules()
    core_ok = check_core_functionality()
    files_ok = check_program_files()
    
    # 总结结果
    print("\n📊 检查结果总结")
    print("=" * 40)
    
    if python_ok and modules_ok and core_ok and files_ok:
        print("🎉 恭喜！所有检查都通过了")
        print("✅ 程序可以正常运行")
        print("\n启动程序:")
        print("  python main.py")
    else:
        print("❌ 发现问题，需要解决以下问题:")
        if not python_ok:
            print("  - Python版本不符合要求")
        if not modules_ok:
            print("  - 缺少必需的模块")
        if not core_ok:
            print("  - 核心功能测试失败")
        if not files_ok:
            print("  - 程序文件不完整")
        
        provide_solutions()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
