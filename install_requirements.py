#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包安装脚本
用于在新的虚拟环境中安装所有必要的依赖包
"""

import subprocess
import sys
import os

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    if description:
        print(f"正在执行: {description}")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✓ 执行成功")
            return True
        else:
            print(f"✗ 执行失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"✗ 执行异常: {str(e)}")
        return False

def check_python_environment():
    """检查Python环境"""
    print("检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("✗ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
        return True

def upgrade_pip():
    """升级pip"""
    print("\n升级pip...")
    return run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")

def install_package(package, description="", use_mirror=False):
    """安装单个包"""
    mirror_url = "https://pypi.tuna.tsinghua.edu.cn/simple"
    
    if use_mirror:
        command = f"{sys.executable} -m pip install -i {mirror_url} {package}"
        desc = f"{description} (使用国内镜像)"
    else:
        command = f"{sys.executable} -m pip install {package}"
        desc = description
    
    success = run_command(command, desc)
    
    # 如果失败且没有使用镜像，尝试使用镜像
    if not success and not use_mirror:
        print("尝试使用国内镜像重新安装...")
        return install_package(package, description, use_mirror=True)
    
    return success

def install_requirements():
    """安装所有依赖包"""
    packages = [
        ("requests>=2.25.0", "HTTP请求库"),
        ("urllib3>=1.26.0", "HTTP客户端库"),
        ("certifi>=2021.5.25", "SSL证书库")
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package, description in packages:
        print(f"\n正在安装 {package} ({description})...")
        if install_package(package, description):
            success_count += 1
        else:
            print(f"✗ {package} 安装失败")
    
    print(f"\n安装结果: {success_count}/{total_count} 个包安装成功")
    return success_count == total_count

def verify_installation():
    """验证安装结果"""
    print("\n" + "="*50)
    print("验证安装结果...")
    print("="*50)
    
    # 检查已安装的包
    run_command(f"{sys.executable} -m pip list", "查看已安装的包")
    
    # 测试导入模块
    modules_to_test = [
        ("requests", "HTTP请求库"),
        ("urllib3", "HTTP客户端库"),
        ("certifi", "SSL证书库"),
        ("tkinter", "GUI库"),
        ("json", "JSON处理库"),
        ("configparser", "配置文件库"),
        ("threading", "线程库"),
        ("datetime", "日期时间库"),
        ("re", "正则表达式库"),
        ("hashlib", "哈希库"),
        ("uuid", "UUID库")
    ]
    
    print("\n测试模块导入:")
    success_count = 0
    
    for module, description in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module} ({description}) - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module} ({description}) - 导入失败: {e}")
    
    print(f"\n模块测试结果: {success_count}/{len(modules_to_test)} 个模块可用")
    
    # 测试核心功能
    print("\n测试核心功能...")
    try:
        # 测试配置管理
        import configparser
        config = configparser.ConfigParser()
        print("✓ 配置管理功能正常")
        
        # 测试网络请求
        import requests
        print("✓ 网络请求功能正常")
        
        # 测试GUI
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✓ GUI功能正常")
        
        print("✓ 所有核心功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 核心功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("           随机邮箱程序 - 依赖包安装脚本")
    print("="*60)
    
    # 检查Python环境
    if not check_python_environment():
        print("\n❌ Python环境检查失败，请检查Python安装")
        input("按回车键退出...")
        return
    
    # 升级pip
    if not upgrade_pip():
        print("\n⚠️ pip升级失败，但可以继续安装")
    
    # 安装依赖包
    if install_requirements():
        print("\n✅ 所有依赖包安装成功！")
    else:
        print("\n⚠️ 部分依赖包安装失败，但程序可能仍可运行")
    
    # 验证安装
    if verify_installation():
        print("\n🎉 安装验证通过！程序可以正常运行")
        print("\n现在你可以运行程序了:")
        print("  方法1: 双击 run.bat")
        print("  方法2: 运行 python main.py")
    else:
        print("\n❌ 安装验证失败，请检查错误信息")
    
    print("\n" + "="*60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
