#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tempmail.plus功能演示脚本
展示新的*@域名格式和自定义PIN码功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.email_generator import EmailGenerator
from src.services.temp_mail_service import TempMailService

def demo_header():
    """显示演示标题"""
    print("=" * 60)
    print("🎯 Tempmail.plus 功能演示")
    print("=" * 60)
    print("新功能:")
    print("✅ 支持 *@域名 格式的邮箱")
    print("✅ 用户自定义PIN码")
    print("✅ tempmail.plus服务集成")
    print("✅ 智能服务识别")
    print("=" * 60)
    print()

def demo_email_generation():
    """演示邮箱生成功能"""
    print("📧 邮箱生成演示")
    print("-" * 30)
    
    generator = EmailGenerator()
    
    # 演示tempmail.plus格式
    print("1. Tempmail.plus格式 (*@域名):")
    email1 = generator.generate_email_address("tempmail.plus", use_asterisk=True)
    print(f"   生成邮箱: {email1}")
    print(f"   实际前缀: {generator.get_actual_prefix()}")
    
    # 演示自定义前缀
    email2 = generator.generate_email_address("tempmail.plus", "demo2024", use_asterisk=True)
    print(f"   自定义前缀: {email2}")
    print(f"   实际前缀: {generator.get_actual_prefix()}")
    
    # 演示传统格式
    print("\n2. 传统格式 (用户名@域名):")
    email3 = generator.generate_email_address("temp-mail.org", "testuser", use_asterisk=False)
    print(f"   传统邮箱: {email3}")
    
    print()

def demo_pin_functionality():
    """演示PIN码功能"""
    print("🔐 PIN码功能演示")
    print("-" * 30)
    
    generator = EmailGenerator()
    
    # 演示自定义PIN码
    print("1. 自定义PIN码 (用于tempmail.plus):")
    custom_pin = "demo123456"
    set_pin = generator.set_custom_pin(custom_pin)
    print(f"   设置PIN码: {set_pin}")
    print(f"   获取PIN码: {generator.get_current_pin()}")
    
    # 演示随机PIN码
    print("\n2. 随机PIN码 (用于其他服务):")
    random_pin = generator.generate_pin_code(8)
    print(f"   随机PIN码: {random_pin}")
    
    print()

def demo_service_integration():
    """演示服务集成"""
    print("🌐 服务集成演示")
    print("-" * 30)
    
    service = TempMailService()
    
    # 演示域名获取
    print("1. 可用域名:")
    tempmail_domains = service.get_available_domains("tempmail.plus")
    print(f"   Tempmail.plus: {tempmail_domains}")
    
    other_domains = service.get_available_domains("temp-mail.org")
    print(f"   Temp-mail.org: {other_domains[:3]}...")  # 只显示前3个
    
    # 演示邮箱创建
    print("\n2. 邮箱创建:")
    email_info = service.create_email("tempmail.plus", "demouser")
    print(f"   创建结果: {email_info}")
    
    print()

def demo_config_management():
    """演示配置管理"""
    print("⚙️ 配置管理演示")
    print("-" * 30)
    
    config = ConfigManager("demo_config.ini")
    
    # 演示配置设置
    print("1. 配置设置:")
    config.set_preferred_domain("tempmail.plus")
    config.set_pin_code("demo654321")
    config.set_custom_email_prefix("demouser")
    
    print(f"   首选域名: {config.get_preferred_domain()}")
    print(f"   PIN码: {config.get_pin_code()}")
    print(f"   邮箱前缀: {config.get_custom_email_prefix()}")
    
    # 演示域名列表
    print("\n2. 域名列表:")
    domains = config.get_custom_domains()
    print(f"   支持的域名: {domains}")
    
    print()

def demo_workflow():
    """演示完整工作流程"""
    print("🔄 完整工作流程演示")
    print("-" * 30)
    
    # 初始化组件
    config = ConfigManager("demo_config.ini")
    generator = EmailGenerator()
    service = TempMailService()
    
    print("步骤1: 配置tempmail.plus服务")
    config.set_preferred_domain("tempmail.plus")
    user_pin = "workflow123"
    config.set_pin_code(user_pin)
    print(f"   ✓ 设置域名: {config.get_preferred_domain()}")
    print(f"   ✓ 设置PIN码: {user_pin}")
    
    print("\n步骤2: 生成邮箱地址")
    domain = config.get_preferred_domain()
    custom_prefix = "workflow2024"
    email = generator.generate_email_address(domain, custom_prefix, use_asterisk=True)
    pin = generator.set_custom_pin(user_pin)
    print(f"   ✓ 生成邮箱: {email}")
    print(f"   ✓ 设置PIN码: {pin}")
    print(f"   ✓ 实际前缀: {generator.get_actual_prefix()}")
    
    print("\n步骤3: 模拟邮件操作")
    print(f"   ✓ 邮箱地址: {email}")
    print(f"   ✓ 访问PIN码: {pin}")
    print(f"   ✓ 可以开始接收邮件")
    
    print("\n步骤4: 验证配置持久化")
    saved_domain = config.get_preferred_domain()
    saved_pin = config.get_pin_code()
    print(f"   ✓ 保存的域名: {saved_domain}")
    print(f"   ✓ 保存的PIN码: {saved_pin}")
    
    print()

def demo_comparison():
    """演示功能对比"""
    print("📊 功能对比演示")
    print("-" * 30)
    
    generator = EmailGenerator()
    
    print("Tempmail.plus vs 传统服务:")
    print()
    
    # Tempmail.plus
    print("🔒 Tempmail.plus:")
    tempmail_email = generator.generate_email_address("tempmail.plus", "compare", use_asterisk=True)
    tempmail_pin = generator.set_custom_pin("compare123")
    print(f"   邮箱格式: {tempmail_email}")
    print(f"   PIN码类型: 用户自定义 ({tempmail_pin})")
    print(f"   隐私级别: 高 (加密)")
    print(f"   实际前缀: {generator.get_actual_prefix()}")
    
    print()
    
    # 传统服务
    print("🔓 传统服务:")
    traditional_email = generator.generate_email_address("temp-mail.org", "compare", use_asterisk=False)
    traditional_pin = generator.generate_pin_code()
    print(f"   邮箱格式: {traditional_email}")
    print(f"   PIN码类型: 自动生成 ({traditional_pin})")
    print(f"   隐私级别: 中等")
    print(f"   实际前缀: compare")
    
    print()

def cleanup_demo():
    """清理演示文件"""
    demo_files = ["demo_config.ini"]
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)

def main():
    """主演示函数"""
    try:
        demo_header()
        demo_email_generation()
        demo_pin_functionality()
        demo_service_integration()
        demo_config_management()
        demo_workflow()
        demo_comparison()
        
        print("🎉 演示完成！")
        print()
        print("主要改进:")
        print("✅ 支持 *@域名 格式 (tempmail.plus)")
        print("✅ 用户自定义PIN码")
        print("✅ 智能服务识别")
        print("✅ 配置持久化")
        print("✅ 向后兼容")
        print()
        print("现在可以启动主程序体验新功能:")
        print("python main.py")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_demo()

if __name__ == "__main__":
    main()
