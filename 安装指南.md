# 随机邮箱程序 - 安装指南

## 🚀 快速安装

### 方法一：自动安装 (推荐)

1. **使用Python环境**
   ```bash
   python install_requirements.py
   ```

2. **使用Conda环境**
   ```bash
   # Windows
   install_conda.bat
   
   # 或者手动运行
   conda install -c conda-forge requests urllib3 certifi -y
   ```

3. **使用批处理文件**
   ```bash
   # Windows
   install_requirements.bat
   ```

### 方法二：手动安装

```bash
# 使用pip安装
pip install requests>=2.25.0 urllib3>=1.26.0 certifi>=2021.5.25

# 或者使用requirements文件
pip install -r requirements_simple.txt

# 使用conda安装
conda install -c conda-forge requests urllib3 certifi
```

## 📋 依赖包说明

### 必需的包
- **requests** (>=2.25.0) - HTTP请求库，用于调用邮箱API
- **urllib3** (>=1.26.0) - HTTP客户端库，requests的底层依赖
- **certifi** (>=2021.5.25) - SSL证书库，用于HTTPS连接

### 内置模块 (无需安装)
- **tkinter** - GUI界面库
- **json** - JSON数据处理
- **configparser** - 配置文件管理
- **threading** - 多线程支持
- **datetime** - 日期时间处理
- **re** - 正则表达式
- **hashlib** - 哈希算法
- **uuid** - UUID生成
- **base64** - Base64编码
- **os, sys** - 系统接口

## 🔧 环境要求

- **Python版本**: 3.7 或更高
- **操作系统**: Windows 10/11 (主要测试平台)
- **网络**: 需要互联网连接访问邮箱API

## 🐍 虚拟环境设置

### 使用venv创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate

# 安装依赖
pip install -r requirements_simple.txt

# 运行程序
python main.py
```

### 使用conda创建虚拟环境

```bash
# 创建conda环境
conda create -n random_email python=3.9

# 激活环境
conda activate random_email

# 安装依赖
conda install -c conda-forge requests urllib3 certifi

# 运行程序
python main.py
```

## 🛠️ 故障排除

### 常见问题

**Q1: 提示"ModuleNotFoundError: No module named 'requests'"**
```bash
# 解决方案
pip install requests
# 或者
python install_requirements.py
```

**Q2: SSL证书错误**
```bash
# 解决方案
pip install --upgrade certifi
# 或者
conda update certifi
```

**Q3: tkinter导入失败**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
# 或者
sudo dnf install python3-tkinter

# Windows (通常已包含)
# 重新安装Python，确保勾选"tcl/tk and IDLE"选项
```

**Q4: 权限错误**
```bash
# 使用用户安装
pip install --user requests urllib3 certifi

# 或者使用管理员权限运行命令提示符
```

### 网络问题

如果遇到网络连接问题，可以使用国内镜像：

```bash
# 使用清华镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple requests urllib3 certifi

# 使用阿里镜像
pip install -i https://mirrors.aliyun.com/pypi/simple/ requests urllib3 certifi

# 使用豆瓣镜像
pip install -i https://pypi.douban.com/simple/ requests urllib3 certifi
```

## ✅ 验证安装

运行以下命令验证安装是否成功：

```python
# 测试脚本
python -c "
import requests
import tkinter as tk
import json
import configparser
print('✅ 所有依赖包安装成功！')
print('🎉 程序可以正常运行')
"
```

或者运行测试脚本：
```bash
python test_core_functions.py
```

## 🚀 运行程序

安装完成后，可以通过以下方式运行程序：

1. **双击运行** (Windows)
   ```
   双击 run.bat 文件
   ```

2. **命令行运行**
   ```bash
   python main.py
   ```

3. **在虚拟环境中运行**
   ```bash
   # 激活虚拟环境
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   
   # 运行程序
   python main.py
   ```

## 📞 获取帮助

如果遇到问题：

1. 检查Python版本：`python --version`
2. 检查已安装的包：`pip list` 或 `conda list`
3. 运行测试脚本：`python test_core_functions.py`
4. 查看错误日志并根据提示解决

---

**注意**: 确保在正确的Python环境中安装依赖包，特别是在使用虚拟环境时。
