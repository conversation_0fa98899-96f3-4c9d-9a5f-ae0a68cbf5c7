#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时邮箱服务
集成多个临时邮箱API服务
"""

import requests
import json
import time
import base64
from typing import List, Dict, Optional, Any
from datetime import datetime
import hashlib

class TempMailService:
    """临时邮箱服务类"""
    
    def __init__(self, timeout: int = 10):
        """
        初始化临时邮箱服务
        
        Args:
            timeout: 请求超时时间
        """
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 支持的服务提供商
        self.providers = {
            'temp-mail.org': self._temp_mail_org_handler,
            'guerrillamail.com': self._guerrilla_mail_handler,
            '10minutemail.com': self._ten_minute_mail_handler,
            'mailinator.com': self._mailinator_handler
        }
    
    def get_available_domains(self, provider: str = 'temp-mail.org') -> List[str]:
        """
        获取可用域名列表
        
        Args:
            provider: 服务提供商
            
        Returns:
            域名列表
        """
        try:
            if provider == 'temp-mail.org':
                return self._get_temp_mail_domains()
            elif provider == 'guerrillamail.com':
                return ['guerrillamail.com', 'guerrillamail.de', 'guerrillamail.net']
            elif provider == '10minutemail.com':
                return ['10minutemail.com']
            elif provider == 'mailinator.com':
                return ['mailinator.com']
            else:
                return ['temp-mail.org']
        except Exception as e:
            print(f"获取域名失败: {e}")
            return ['temp-mail.org']
    
    def create_email(self, domain: str, custom_name: str = None) -> Dict[str, Any]:
        """
        创建临时邮箱
        
        Args:
            domain: 邮箱域名
            custom_name: 自定义邮箱名（可选）
            
        Returns:
            邮箱信息字典
        """
        provider = self._get_provider_from_domain(domain)
        handler = self.providers.get(provider)
        
        if handler:
            return handler('create', domain=domain, custom_name=custom_name)
        else:
            return self._create_fallback_email(domain, custom_name)
    
    def get_emails(self, email_address: str, pin: str = None) -> List[Dict[str, Any]]:
        """
        获取邮件列表
        
        Args:
            email_address: 邮箱地址
            pin: PIN码（如果需要）
            
        Returns:
            邮件列表
        """
        domain = email_address.split('@')[1] if '@' in email_address else ''
        provider = self._get_provider_from_domain(domain)
        handler = self.providers.get(provider)
        
        if handler:
            return handler('get_emails', email=email_address, pin=pin)
        else:
            return self._get_fallback_emails(email_address)
    
    def get_email_content(self, email_id: str, email_address: str) -> Dict[str, Any]:
        """
        获取邮件详细内容
        
        Args:
            email_id: 邮件ID
            email_address: 邮箱地址
            
        Returns:
            邮件内容
        """
        domain = email_address.split('@')[1] if '@' in email_address else ''
        provider = self._get_provider_from_domain(domain)
        handler = self.providers.get(provider)
        
        if handler:
            return handler('get_content', email_id=email_id, email=email_address)
        else:
            return {'subject': '无法获取', 'content': '邮件内容获取失败', 'from': '未知'}
    
    def _get_provider_from_domain(self, domain: str) -> str:
        """根据域名获取服务提供商"""
        if 'temp-mail' in domain:
            return 'temp-mail.org'
        elif 'guerrilla' in domain:
            return 'guerrillamail.com'
        elif '10minute' in domain:
            return '10minutemail.com'
        elif 'mailinator' in domain:
            return 'mailinator.com'
        else:
            return 'temp-mail.org'
    
    def _temp_mail_org_handler(self, action: str, **kwargs) -> Any:
        """Temp-mail.org API处理器"""
        base_url = "https://api.temp-mail.org/request"
        
        try:
            if action == 'create':
                domain = kwargs.get('domain', 'temp-mail.org')
                custom_name = kwargs.get('custom_name')
                
                if custom_name:
                    email = f"{custom_name}@{domain}"
                else:
                    # 生成随机邮箱
                    response = self.session.get(f"{base_url}/mail/id/", timeout=self.timeout)
                    if response.status_code == 200:
                        email = response.text.strip()
                    else:
                        email = f"temp{int(time.time())}@{domain}"
                
                return {
                    'email': email,
                    'created_at': datetime.now(),
                    'provider': 'temp-mail.org'
                }
            
            elif action == 'get_emails':
                email = kwargs.get('email')
                if not email:
                    return []
                
                # 获取邮件列表
                email_hash = hashlib.md5(email.encode()).hexdigest()
                response = self.session.get(
                    f"{base_url}/mail/id/{email_hash}/",
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    try:
                        emails = response.json()
                        return self._format_temp_mail_emails(emails)
                    except json.JSONDecodeError:
                        return []
                return []
            
            elif action == 'get_content':
                email_id = kwargs.get('email_id')
                response = self.session.get(
                    f"{base_url}/one_mail/id/{email_id}/",
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    try:
                        content = response.json()
                        return self._format_temp_mail_content(content)
                    except json.JSONDecodeError:
                        return {}
                return {}
                
        except Exception as e:
            print(f"Temp-mail.org API错误: {e}")
            return [] if action == 'get_emails' else {}
    
    def _guerrilla_mail_handler(self, action: str, **kwargs) -> Any:
        """Guerrilla Mail API处理器"""
        # 这里实现Guerrilla Mail的API调用
        # 由于API限制，这里提供模拟实现
        if action == 'create':
            domain = kwargs.get('domain', 'guerrillamail.com')
            custom_name = kwargs.get('custom_name', f"temp{int(time.time())}")
            return {
                'email': f"{custom_name}@{domain}",
                'created_at': datetime.now(),
                'provider': 'guerrillamail.com'
            }
        elif action == 'get_emails':
            return []  # 模拟空邮件列表
        else:
            return {}
    
    def _ten_minute_mail_handler(self, action: str, **kwargs) -> Any:
        """10 Minute Mail API处理器"""
        # 模拟实现
        if action == 'create':
            domain = kwargs.get('domain', '10minutemail.com')
            custom_name = kwargs.get('custom_name', f"temp{int(time.time())}")
            return {
                'email': f"{custom_name}@{domain}",
                'created_at': datetime.now(),
                'provider': '10minutemail.com'
            }
        elif action == 'get_emails':
            return []
        else:
            return {}
    
    def _mailinator_handler(self, action: str, **kwargs) -> Any:
        """Mailinator API处理器"""
        # 模拟实现
        if action == 'create':
            domain = kwargs.get('domain', 'mailinator.com')
            custom_name = kwargs.get('custom_name', f"temp{int(time.time())}")
            return {
                'email': f"{custom_name}@{domain}",
                'created_at': datetime.now(),
                'provider': 'mailinator.com'
            }
        elif action == 'get_emails':
            return []
        else:
            return {}
    
    def _get_temp_mail_domains(self) -> List[str]:
        """获取temp-mail.org可用域名"""
        try:
            response = self.session.get(
                "https://api.temp-mail.org/request/domains/",
                timeout=self.timeout
            )
            if response.status_code == 200:
                domains = response.json()
                return [domain.lstrip('@') for domain in domains]
        except Exception:
            pass
        
        # 返回默认域名
        return ['temp-mail.org', 'tempmail.net', 'tempmail.org']
    
    def _format_temp_mail_emails(self, emails: List[Dict]) -> List[Dict[str, Any]]:
        """格式化temp-mail邮件数据"""
        formatted = []
        for email in emails:
            formatted.append({
                'id': email.get('mail_id', ''),
                'from': email.get('mail_from', ''),
                'subject': email.get('mail_subject', ''),
                'date': email.get('mail_date', ''),
                'preview': email.get('mail_preview', ''),
                'read': False
            })
        return formatted
    
    def _format_temp_mail_content(self, content: Dict) -> Dict[str, Any]:
        """格式化temp-mail邮件内容"""
        return {
            'subject': content.get('mail_subject', ''),
            'from': content.get('mail_from', ''),
            'date': content.get('mail_date', ''),
            'content': content.get('mail_text', ''),
            'html_content': content.get('mail_html', ''),
            'attachments': content.get('mail_attachments', [])
        }
    
    def _create_fallback_email(self, domain: str, custom_name: str = None) -> Dict[str, Any]:
        """创建备用邮箱（当API不可用时）"""
        if custom_name:
            email = f"{custom_name}@{domain}"
        else:
            email = f"temp{int(time.time())}@{domain}"
        
        return {
            'email': email,
            'created_at': datetime.now(),
            'provider': 'fallback'
        }
    
    def _get_fallback_emails(self, email_address: str) -> List[Dict[str, Any]]:
        """获取备用邮件列表（模拟数据）"""
        return [
            {
                'id': 'demo1',
                'from': '<EMAIL>',
                'subject': '欢迎使用随机邮箱程序',
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'preview': '这是一封测试邮件...',
                'read': False
            }
        ]
