@echo off
chcp 65001 >nul
title 安装依赖包 (Conda环境)

echo ================================
echo   随机邮箱程序 - Conda环境安装
echo ================================
echo.

echo 检查Conda环境...
conda --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Conda，请先安装Anaconda或Miniconda
    pause
    exit /b 1
)

echo 当前Conda环境:
conda info --envs | findstr "*"

echo.
echo 正在安装依赖包...
echo.

echo [1/3] 安装requests...
conda install -c conda-forge requests -y
if errorlevel 1 (
    echo 使用pip安装requests...
    pip install requests>=2.25.0
)

echo.
echo [2/3] 安装urllib3...
conda install -c conda-forge urllib3 -y
if errorlevel 1 (
    echo 使用pip安装urllib3...
    pip install urllib3>=1.26.0
)

echo.
echo [3/3] 安装certifi...
conda install -c conda-forge certifi -y
if errorlevel 1 (
    echo 使用pip安装certifi...
    pip install certifi>=2021.5.25
)

echo.
echo ================================
echo 验证安装结果...
echo ================================

echo.
echo 检查已安装的包:
conda list | findstr -i "requests urllib3 certifi"

echo.
echo 测试导入模块:
python -c "import requests; print('✓ requests 导入成功')" 2>nul || echo "✗ requests 导入失败"
python -c "import urllib3; print('✓ urllib3 导入成功')" 2>nul || echo "✗ urllib3 导入失败"
python -c "import certifi; print('✓ certifi 导入成功')" 2>nul || echo "✗ certifi 导入失败"
python -c "import tkinter; print('✓ tkinter 导入成功')" 2>nul || echo "✗ tkinter 导入失败"

echo.
echo ================================
echo 安装完成！
echo ================================
echo.
echo 现在你可以运行程序了:
echo   方法1: 双击 run.bat
echo   方法2: 运行 python main.py
echo.
pause
