#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责管理用户配置，包括自定义域名、PIN码等
"""

import configparser
import os
import json
from typing import Dict, List, Optional

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['DEFAULT'] = {
            'custom_domains': '["tempmail.plus", "tempmail.email", "tempmail.ninja", "temp-mail.org", "guerrillamail.com", "10minutemail.com"]',
            'default_domain': 'tempmail.plus',
            'auto_refresh_interval': '30',
            'max_emails_display': '50'
        }
        
        self.config['USER'] = {
            'custom_email_prefix': '',
            'pin_code': '',
            'preferred_domain': 'temp-mail.org'
        }
        
        self.config['ADVANCED'] = {
            'api_timeout': '10',
            'retry_attempts': '3',
            'debug_mode': 'False'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get_custom_domains(self) -> List[str]:
        """获取自定义域名列表"""
        domains_str = self.config.get('DEFAULT', 'custom_domains', fallback='[]')
        try:
            return json.loads(domains_str)
        except json.JSONDecodeError:
            return ["tempmail.plus", "tempmail.email", "tempmail.ninja", "temp-mail.org", "guerrillamail.com", "10minutemail.com"]
    
    def set_custom_domains(self, domains: List[str]):
        """设置自定义域名列表"""
        self.config.set('DEFAULT', 'custom_domains', json.dumps(domains))
        self.save_config()
    
    def get_default_domain(self) -> str:
        """获取默认域名"""
        return self.config.get('DEFAULT', 'default_domain', fallback='tempmail.plus')
    
    def set_default_domain(self, domain: str):
        """设置默认域名"""
        self.config.set('DEFAULT', 'default_domain', domain)
        self.save_config()
    
    def get_custom_email_prefix(self) -> str:
        """获取自定义邮箱前缀"""
        return self.config.get('USER', 'custom_email_prefix', fallback='')
    
    def set_custom_email_prefix(self, prefix: str):
        """设置自定义邮箱前缀"""
        self.config.set('USER', 'custom_email_prefix', prefix)
        self.save_config()
    
    def get_pin_code(self) -> str:
        """获取PIN码"""
        return self.config.get('USER', 'pin_code', fallback='')
    
    def set_pin_code(self, pin: str):
        """设置PIN码"""
        self.config.set('USER', 'pin_code', pin)
        self.save_config()
    
    def get_preferred_domain(self) -> str:
        """获取首选域名"""
        return self.config.get('USER', 'preferred_domain', fallback='tempmail.plus')
    
    def set_preferred_domain(self, domain: str):
        """设置首选域名"""
        self.config.set('USER', 'preferred_domain', domain)
        self.save_config()
    
    def get_auto_refresh_interval(self) -> int:
        """获取自动刷新间隔（秒）"""
        return self.config.getint('DEFAULT', 'auto_refresh_interval', fallback=30)
    
    def set_auto_refresh_interval(self, interval: int):
        """设置自动刷新间隔"""
        self.config.set('DEFAULT', 'auto_refresh_interval', str(interval))
        self.save_config()
    
    def get_max_emails_display(self) -> int:
        """获取最大邮件显示数量"""
        return self.config.getint('DEFAULT', 'max_emails_display', fallback=50)
    
    def set_max_emails_display(self, count: int):
        """设置最大邮件显示数量"""
        self.config.set('DEFAULT', 'max_emails_display', str(count))
        self.save_config()
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.config.getboolean('ADVANCED', 'debug_mode', fallback=False)
    
    def set_debug_mode(self, debug: bool):
        """设置调试模式"""
        self.config.set('ADVANCED', 'debug_mode', str(debug))
        self.save_config()
